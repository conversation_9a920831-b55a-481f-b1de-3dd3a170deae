{"name": "react", "version": "1.0.0", "description": "this is react", "keywords": ["react", "namaste", "react"], "license": "ISC", "author": "\"sanchay baghel\"", "type": "commonjs", "scripts": {"start": "parcel index.html", "build": "parcel build index.html", "test": "jest"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@parcel/babel-preset-env": "^2.14.1", "@testing-library/react": "^16.2.0", "autoprefixer": "^10.4.20", "babel-jest": "^29.7.0", "buffer": "^6.0.3", "jest": "^29.7.0", "parcel": "^2.13.3", "postcss": "^8.5.3", "process": "^0.11.10"}, "dependencies": {"@reduxjs/toolkit": "^2.5.1", "@tailwindcss/postcss": "^4.0.7", "entities": "^6.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^6.29.0", "tailwindcss": "^4.0.7"}}