...
# Food Ordering app
/**
 * Header
 *   -Logo
 *   -Nav Items
 * Body
 *   -Search
 *   -Restaurant Container
 *      -Restaurant Cart
 *         -img
 *         -Name of Res , Start Rating , cuisine,delievery time
 * Footer
 *  -Copyright
 *  -Links
 *  -Address
 *  -Contact
 */

 there are two types of export 
 one is default export and one is named export 
 if export is default then import is without curly braces
 if export is nakmed the import is with curly braces 

 # React Hooks
 (Normal Js utility fumctions)
 - useState() -StateVariable State in React
 - useEffect()
  # why react is fast 
  - react is fast because it do efficient dom manipulation 
# how
 react combie the virtual dom concept 
 whenever there¸ is difference find between actual dom and virtual dom and actual dom it automatically update the actual dom without any delayy 
 # if has diff algorithm
 which is re-render ui when any update happen

 # Routing in web apps
 - client side Routing 
 - Server side Routing
 # Client Side Routing
 - in client side routing only one page or one compontents get interchanget
 - but in server side routing whole page get a network call again and again 
- in client side routing network already happen in initial so it does not need to make further call 
this is also call a single page application
# Redux Toolkit
 -Install @reduxjs/toolkit and react-redux
 -Build our store
 -Connect our store to our app
 -Slice (cardSlic)
 -dispatch (action)
 -Selector
