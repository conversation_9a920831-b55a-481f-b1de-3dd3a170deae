* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none;
}

body {
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}
a{
    color: inherit;
    text-decoration: none;
    
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center; 
    padding: 10px 20px;
    border-bottom: 2px solid #ccc;
    background-color: #4CAF50;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky; 
    top: 0; 
    z-index: 1000; 
    height: 100px;
}

.logo {
    width: 100px;
}

.nav-items {
    padding: 0px 20px;
}

.nav-items > ul {
    font-size: 18px;
    display: flex;
    list-style-type: none;
    background-color: transparent;
}

.nav-items > ul > li {
    padding: 10px 15px;
    margin: 0 5px;
    text-decoration: none;
    color: white;
    background-color: transparent;
    transition: background-color 0.2s ease,color 0.3s ease;
}
.nav-items > ul > li:hover {
    background-color: #45a049; 
    color: #fff; 
}
.nav-items > ul > li a {
    color: inherit;
    text-decoration: none; 
}
.nav-items > ul > li a:hover {
    color: #fff; 
}

.res-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.res-card {
    background-color: #ddd;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex; 
    flex-direction: column; 
    justify-content: space-between; 
    text-align: center;
    height: 450px; 
    width: 250px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

h4, h3 {
    color: rgb(29, 16, 29);
    text-decoration: none;
}

.res-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 0;
}

.res-logo {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.res-title {
    font-size: 1.2em;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
    word-wrap: break-word;
}

.res-details {
    font-size: 0.9em;
    color: #777;
    margin-bottom: 15px;
    overflow-wrap: break-word;
    flex-grow: 1; 
    overflow: hidden;
    text-overflow: ellipsis; 
    display: -webkit-box; 
    text-decoration: none;
}

.res-rating {
    font-size: 1em;
    font-weight: bold;
    color: #444;
}

.filter-btn {
    margin: 10px;
    cursor: pointer;
}

.shimmer-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    padding: 16px;
}

/* Individual shimmer card */
.shimmer-card {
    width: 100%;
    height: 200px;
    border-radius: 12px;
    background: linear-gradient(
        90deg,
        #f0f0f0 25%,
        #e0e0e0 50%,
        #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Shimmer animation */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.login-btn {
   
    width: auto;
    position: relative;
    cursor: pointer;
    background-color: transparent;
    border: 0px;
    font-size: 18px;
}

.filter {
    display: flex;
}

.search-box {
    width: 100%;
    padding: 10px;
    margin: 10px 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
    background-color: palegoldenrod;
}
.search-button{
    margin-left: 5px;
    padding: 10px 16px;
    cursor: pointer;
}
.search-button:hover{
    background-color: rgb(27, 81, 63);
}


.star {
    width: 20px;
    height: 23px;
    object-fit: cover;
}

.menu {
    max-width: 800px;
    margin: 20px auto;
    padding: 16px;
    background-color: #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-left: 0;
}


.Menu-Card {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 16px 0;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    cursor: pointer;
}

.Menu-Card h2 {
    font-size: 18px;
    margin-bottom: 12px;
}

/* Menu Item Styling */
.Menu-item ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
   
}

.Menu-item li {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
   
}

.Menu-item .item {
    flex: 1;
}

.Menu-item .item-name {
    font-size: 14px;
    font-weight: bold;
}

.Menu-item .rating {
    font-size: 16px;
    color: #555;
    margin-top: 4px;
}

.Menu-item .desc {
    font-size: 14px;
    color: #777;
    margin-top: 8px;
}

.Menu-item .item-image {
    margin-left: 16px;
}

.Menu-item .item-image .image {
    width: 80px;
    height: 80px;
    overflow: hidden;
    border-radius: 8px;
}

.Menu-item .item-image .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

i {
    position: absolute;
    top: 4px;
    right: 10px;
}
.user-card{
    padding: 10px;
    margin: 4px;
}
 .filter-btn{
    background-color: gray; 
    color: white;
    padding: 0px 20px;
   height: 40px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
    transition: background-color 0.3s ease;
}
.label{
    position: absolute;
    background-color: black;
    color: white;
    z-index: 1;
    border-radius: 2px;
}
.recommended{
    background-color: #ddddddf8;
}
.AddButton{
    padding: 4px 12px;
    background-color: greenyellow;
    border: none;
    margin: auto;
    position: absolute;
}