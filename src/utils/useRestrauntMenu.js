import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const useRestrauntMenu = () => {
  const [resInfo, setResInfo] = useState(null);
  const { resId } = useParams(); 

  useEffect(() => {
    const fetchMenu = async () => {
      try {
        // Try multiple CORS proxies for menu API
        const proxies = [
          "https://cors-anywhere.herokuapp.com/",
          "https://api.codetabs.com/v1/proxy?quest=",
          "https://thingproxy.freeboard.io/fetch/"
        ];

        const targetUrl = `https://www.swiggy.com/dapi/menu/pl?page-type=REGULAR_MENU&complete-menu=true&lat=28.4659992&lng=77.50392149999999&restaurantId=${resId}&catalog_qa=undefined&submitAction=ENTER`;

        // Try each proxy until one works
        for (const proxy of proxies) {
          try {
            const data = await fetch(proxy + targetUrl);
            const json = await data.json();
            setResInfo(json.data);
            return; // Success, exit the loop
          } catch (error) {
            console.warn(`Proxy ${proxy} failed for menu:`, error.message);
            continue;
          }
        }

        throw new Error("All CORS proxies failed for menu");
      } catch (error) {
        console.error("Failed to fetch menu data:", error);
      }
    };
    fetchMenu();
  }, [resId]);

  return resInfo;
};