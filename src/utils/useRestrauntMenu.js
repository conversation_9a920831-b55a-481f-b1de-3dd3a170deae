import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const useRestrauntMenu = () => {
  const [resInfo, setResInfo] = useState(null);
  const { resId } = useParams(); 

  useEffect(() => {
    const fetchMenu = async () => {
      // Using CORS proxy for menu API as well
      const proxyUrl = "https://api.allorigins.win/raw?url=";
      const targetUrl = `https://www.swiggy.com/dapi/menu/pl?page-type=REGULAR_MENU&complete-menu=true&lat=28.4659992&lng=77.50392149999999&restaurantId=${resId}&catalog_qa=undefined&submitAction=ENTER`;

      const data = await fetch(proxyUrl + encodeURIComponent(targetUrl));
      const json = await data.json();
      setResInfo(json.data);
    };
    fetchMenu();
  }, [resId]);

  return resInfo;
};