import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const useRestrauntMenu = () => {
  const [resInfo, setResInfo] = useState(null);
  const { resId } = useParams(); 

  useEffect(() => {
    const fetchMenu = async () => {
      try {
        console.log("🍽️ Fetching menu for restaurant ID:", resId);

        const data = await fetch(
          `https://www.swiggy.com/dapi/menu/pl?page-type=REGULAR_MENU&complete-menu=true&lat=28.4659992&lng=77.50392149999999&restaurantId=${resId}&catalog_qa=undefined&submitAction=ENTER`
        );

        console.log("📡 Menu fetch response:", data);

        if (!data.ok) {
          throw new Error(`HTTP error! status: ${data.status}`);
        }

        const json = await data.json();
        console.log("✅ Menu data received:", json);
        setResInfo(json.data);
      } catch (error) {
        console.error("❌ Menu fetch failed:", error);
        console.error("Error details:", {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }
    };

    if (resId) {
      fetchMenu();
    }
  }, [resId]);

  return resInfo;
};