import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const useRestrauntMenu = () => {
  const [resInfo, setResInfo] = useState(null);
  const { resId } = useParams(); 

  useEffect(() => {
    const fetchMenu = async () => {
      const data = await fetch(
        `https://www.swiggy.com/dapi/menu/pl?page-type=REGULAR_MENU&complete-menu=true&lat=28.4659992&lng=77.50392149999999&restaurantId=${resId}&catalog_qa=undefined&submitAction=ENTER`
      );
      const json = await data.json();
      setResInfo(json.data);
    };
    fetchMenu();
  }, [resId]);

  return resInfo;
};