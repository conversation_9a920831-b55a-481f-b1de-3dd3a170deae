export const fetchData = async () => {
  try {
    console.log("🚀 Attempting to fetch Swiggy data...");

    // Direct API call - requires CORS to be disabled in browser or extension
    const data = await fetch("https://www.swiggy.com/dapi/restaurants/list/v5?lat=28.4659992&lng=77.50392149999999&is-seo-homepage-enabled=true&page_type=DESKTOP_WEB_LISTING");

    if (!data.ok) {
      throw new Error(`HTTP error! status: ${data.status}`);
    }

    const json = await data.json();
    console.log("✅ Successfully fetched Swiggy data:", json);

    return json?.data?.cards[1]?.card?.card?.gridElements?.infoWithStyle?.restaurants;
  } catch (error) {
    console.error("❌ CORS Error - Please enable CORS extension or disable browser security:", error.message);

    // Show user-friendly error message
    alert(`
🚫 CORS Error Detected!

To fix this and access real Swiggy data:

1. Install "CORS Unblock" Chrome extension, OR
2. Use Chrome with disabled security (see console for command)

Error: ${error.message}
    `);

    throw error;
  }
};


