export const fetchData = async () => {
  // Using CORS proxy to access Swiggy API from localhost
  const proxyUrl = "https://api.allorigins.win/raw?url=";
  const targetUrl = "https://www.swiggy.com/dapi/restaurants/list/v5?lat=28.4659992&lng=77.50392149999999&is-seo-homepage-enabled=true&page_type=DESKTOP_WEB_LISTING";

  const data = await fetch(proxyUrl + encodeURIComponent(targetUrl));
  const json = await data.json();
  return json?.data?.cards[1]?.card?.card?.gridElements?.infoWithStyle?.restaurants;
};


