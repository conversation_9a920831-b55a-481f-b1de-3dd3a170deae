import { createSlice } from "@reduxjs/toolkit";

const cartSlice = createSlice({
    name:"cart",
    initialState:{
        items:[],
    },
    reducers:{
        //mutating the state here
        addItem:(state,action)=>{
            state.items.push(action.payload)
        },
        removeItem:(state,action)=>{
            // Remove item by index
            const index = action.payload;
            if (index !== undefined && index >= 0 && index < state.items.length) {
                state.items.splice(index, 1);
            } else {
                // Fallback: remove last item if no index provided
                state.items.pop();
            }
        },
        clearCart:(state,action)=>{
            state.items.length=0;
        },
    },
})

export const {addItem,removeItem,clearCart}=cartSlice.actions;

export default cartSlice.reducer;