import React from "react"
import UserContext from "../utils/UserContext"
import { data } from "autoprefixer"

export class UserClass extends React.Component{
    constructor(props){
        super(props)
        this.state={
            userInfo:{
                name:"dummy name",
                location:"default"
            }
        }
        console.log(this.props.name+"constructor")
    }
   async componentDidMount(){
        console.log(this.props.name+"Child Component Did Mount")
        // const data=await fetch("https://api.github.com/users/sanchaybaghel");
        // const json =await data.json();
        // this.setState({
        //     userInfo:json
        // })
        // console.log(json)

    }
    componentDidUpdate(){
        console.log("component did updated")
    }
    componentWillUnmount(){
        console.log("component will unmoung")
    }
    render(){
       
        console.log(this.props.name+"render")
        const {login,Location,avatar_url}=this.state.userInfo;
        
        
        return(
            <div className="user-card">
                <UserContext.Consumer>
            {(data)=>console.log(data)}
            </UserContext.Consumer>
                <img src={avatar_url}/>
                <h1>{login}</h1>
                <h2>{Location}</h2>
            </div>
        )
    }
}
