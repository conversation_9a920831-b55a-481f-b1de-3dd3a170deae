import { UserClass } from "./UserClass"
import React,{useState,useEffect} from "react"
export const User=()=>{
    const [count,setCount] =useState(1);
    useEffect(()=>{
        const interval=setInterval(()=>{
            setCount((prevCount)=>prevCount+1)
        },1000)
    },[])

    async function getUserInfo() {
        const data=await fetch("https://api.github.com/users/sanchaybaghel");
        const json=await data.json();
        console.log(json)
    }

    return(
        <div className="user-card">
            <h2>Name:Sanchay</h2>
            <button onClick={()=>{
                setCount(count+1)
            }}>Increase count</button>
            <button onClick={()=>{
                setCount(1);
            }}>Reset Count</button>
            <h1>count:{count}</h1>
            <h3>Location</h3>
            <h4>Contact:@Sanchaymay</h4>
        </div>
    )
}
