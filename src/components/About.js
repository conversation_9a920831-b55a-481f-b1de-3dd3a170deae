import { User } from "./User";
import React from "react";
import { UserClass } from "./UserClass";
export class About extends React.Component {
  constructor(props) {
    super(props);
    console.log("parent constructor");
  }
  componentDidMount(){
    console.log("parent mounted called")
  }
  render() {
    console.log("parent render");
    return (
      <div>
        <h1>About class Component</h1>
        <UserClass name="child1" Location="dehradun" />
        <UserClass name="child2" Location="dehradun" />
        <UserClass name="child3" Location="dehradun" />
      </div>
    );
  }
}
