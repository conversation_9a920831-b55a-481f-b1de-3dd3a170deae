import React, { useState } from "react";
import { Menu } from "./Menu";
import { swiggy_link_image } from "../utils/constants";

const ItemsComponent = ({ itemCards }) => {
  console.log(itemCards)
  return (
   
      <div className="List-item">
        <ul>
          {itemCards.map((item) => (
            <li key={item.card.info.id}>
              <div className="cart-item">
                <div className="cart-item-name"> {item.card.info.name}</div>
                <div className="cart-price">Rs {item.card.info.defaultPrice / 100}</div>
                <div className="cart-desc">{item.card.info.description}</div>
              </div>
              <div className="cart-item-image">
                <div className="cart-image">
                  <img
                    src={`${swiggy_link_image}${item.card.info.imageId}`}
                    alt={item.card.info.name}
                  />
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
  );
};

export default ItemsComponent;
