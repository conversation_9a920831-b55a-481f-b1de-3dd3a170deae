import React, { useState } from "react";
import { Menu } from "./Menu";

const ItemsComponent = ({ menus }) => {
  const [expandedIndex, setExpandedIndex] = useState(null);

  const handleToggle = (index) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  return (
    <div>
      {menus.map((menu, index) => (
        <Menu
          key={index}
          title={menu.title}
          itemCards={menu.itemCards}
          rating={menu.rating}
          userCount={menu.userCount}
          desc={menu.desc}
          isExpanded={expandedIndex === index}
          onToggle={() => handleToggle(index)}
        />
      ))}
    </div>
  );
};

export default ItemsComponent;