import React from "react";
import { useDispatch } from "react-redux";
import { addItem } from "../utils/cartSlice";
import { swiggy_link_image } from "../utils/constants";

export const Menu = ({ itemCards, rating, userCount, title, isExpanded, onToggle,desc }) => {
  const dispatch = useDispatch();

  const handleAddItem = (item) => {
    dispatch(addItem(item));
  };

  return (
    <div className="Menu-Card">
    {console.log("itemscart",itemCards)}
      <h2>{title} ({itemCards.length})</h2>
      <i
        className={`fa-solid fa-circle-chevron-${isExpanded ? "up" : "down"}`}
        onClick={onToggle}
        style={{ cursor: "pointer" }}
      ></i>
      {isExpanded && (
        <div className="Menu-item">
          <ul>
            {itemCards.map((item) => (
              <li key={item.card.info.id}>
                <div className="item">
                  <div className="item-name"> {item.card.info.name}</div>
                  <div className="price">Rs {item.card.info.price / 100}</div>
                  <div className="rating">
                    {rating} {userCount}
                  </div>
                  <div className="desc">{item.card.info.description}</div>
                </div>
                <div className="item-image">
                  <div className="image">
                    <img src={`${swiggy_link_image}${item.card.info.imageId}`} alt={item.card.info.name} />
                  </div>
                  <button
                    className="AddButton"
                    onClick={() => handleAddItem(item.card.info)}
                  >
                    Add
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};