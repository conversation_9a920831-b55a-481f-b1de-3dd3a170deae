import { useState ,useContext} from "react";
import { LOGO_URL } from "../utils/constants";
import { Link } from "react-router-dom";
import { useOnlineStatus } from "../utils/useOnlineStatus";
import UserContext from "../utils/UserContext";
export const Header = () => {
    const[btnNameReact,setBtnReact]=useState("Login")
    const OnlineStatus=useOnlineStatus();
    const data=useContext(UserContext)
    return (
        <div className="header">
            <div className="logo-container">
               <Link to="/"><img className="logo" src={LOGO_URL} alt="Logo" /></Link> 
            </div>
            <div className="nav-items">
                <ul>
                    <li><Link to="/">Home</Link></li>
                    <li><Link to="/about">About Us</Link></li>
                    <li><Link to="/contact">Contact Us</Link></li>
                    <li>Cart</li>
                    <li>
                    <button className="login-btn" onClick={()=>{
                            btnNameReact==="Login" ? setBtnReact("LogOut"):setBtnReact("Login")
                    }}>{btnNameReact}</button>
                    </li>
                </ul>
            </div>
        </div>
    );
};