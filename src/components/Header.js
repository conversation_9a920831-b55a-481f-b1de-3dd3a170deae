import { useState, useContext } from "react";
import { useSelector } from "react-redux";
import { LOGO_URL } from "../utils/constants";
import { Link } from "react-router-dom";
import { useOnlineStatus } from "../utils/useOnlineStatus";
import UserContext from "../utils/UserContext";
export const Header = () => {
    const [btnNameReact, setBtnReact] = useState("Login");
    const OnlineStatus = useOnlineStatus();
    const data = useContext(UserContext);

    // Get cart items from Redux store
    const cartItems = useSelector((store) => store.cart.items);
    const cartCount = cartItems.length;
    return (
        <div className="header">
            <div className="logo-container">
               <Link to="/"><img className="logo" src={LOGO_URL} alt="Logo" /></Link> 
            </div>
            <div className="nav-items">
                <ul>
                    <li><Link to="/">Home</Link></li>
                    <li><Link to="/about">About Us</Link></li>
                    <li><Link to="/contact">Contact Us</Link></li>
                    <li className="cart-container">
                        <Link to="/cart" className="cart-link">
                            🛒 Cart
                            {cartCount > 0 && (
                                <span className="cart-count">{cartCount}</span>
                            )}
                        </Link>
                    </li>
                    <li>
                    <button className="login-btn" onClick={()=>{
                            btnNameReact==="Login" ? setBtnReact("LogOut"):setBtnReact("Login")
                    }}>{btnNameReact}</button>
                    </li>
                </ul>
            </div>
        </div>
    );
};