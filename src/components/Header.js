import { useState ,useContext} from "react";
import { LOGO_URL } from "../utils/constants";
import { Link } from "react-router-dom";
import { useOnlineStatus } from "../utils/useOnlineStatus";
import UserContext from "../utils/UserContext";
import { FaCartShopping } from "react-icons/fa6";
import { useSelector } from "react-redux";
export const Header = () => {
    const cartItems = useSelector((store) => store.cart.items);
    const[btnNameReact,setBtnReact]=useState("Login")
    const OnlineStatus=useOnlineStatus();
    const data=useContext(UserContext)
    console.log(cartItems)
    return (
        <div className="header">
            <div className="logo-container">
               <Link to="/"><img className="logo" src={LOGO_URL} alt="Logo" /></Link> 
            </div>
            <div className="nav-items">
                <ul>
                    <li><Link to="/">Home</Link></li>
                    <li><Link to="/about">About Us</Link></li>
                    <li><Link to="/contact">Contact Us</Link></li>
                    {
                        cartItems.length===0?<li>
                            <Link to={"/cart"}><FaCartShopping /></Link>
                            </li>:(
                            <li>
                                <Link to={"/cart"}> <FaCartShopping />({cartItems.length})</Link>
                            </li>
                        )
                    }
                    <li>
                    <button className="login-btn" onClick={()=>{
                            btnNameReact==="Login" ? setBtnReact("LogOut"):setBtnReact("Login")
                    }}>{btnNameReact}</button>
                    </li>
                </ul>
            </div>
        </div>
    );
};