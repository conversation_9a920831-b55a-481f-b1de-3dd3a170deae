import { Shimmer } from "./Shimmer";
import { useParams } from "react-router-dom";
import { Menu } from "./Menu";
import { useRestrauntMenu } from "../utils/useRestrauntMenu";
import { useState } from "react";

export const RestaurantMenu = () => {
  const resInfo = useRestrauntMenu();
  const [expandedIndex, setExpandedIndex] = useState(null);

  if (!resInfo) {
    return <Shimmer />;
  }

  const { cards } = resInfo?.cards[4]?.groupedCard?.cardGroupMap?.REGULAR;
  const { name, cuisines, costForTwoMessage, avgRating, totalRatingsString } =
    resInfo?.cards[2]?.card?.card?.info || {};

  const handleToggle = (index) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  return (
    <div className="menu">
      <h1>{name}</h1>
      <h3>{cuisines?.join(", ")}</h3>
      <h3>{costForTwoMessage}</h3>
      <h2>Menu</h2>
      <div className="recommended">
        {cards?.slice(1).map((card, index) => {
          const item = resInfo?.cards[4]?.groupedCard?.cardGroupMap?.REGULAR?.cards[index]?.card?.card.itemCards;
          const { itemCards, description, title } =
            resInfo?.cards[4]?.groupedCard?.cardGroupMap?.REGULAR?.cards[index]?.card?.card || {};
          if (item !== undefined) {
            return (
              <Menu
                key={index}
                title={title}
                itemCards={item}
                rating={avgRating}
                userCount={totalRatingsString}
                isExpanded={expandedIndex === index}
                onToggle={() => handleToggle(index)}
                desc={description}
              />
            );
          }
          return null;
        })}
      </div>
    </div>
  );
};