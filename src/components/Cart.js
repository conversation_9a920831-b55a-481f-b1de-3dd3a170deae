import { useDispatch, useSelector } from "react-redux";
import { clearCart, removeItem } from "../utils/cartSlice";
import { swiggy_link_image } from "../utils/constants";

const Cart = () => {
    const cartItems = useSelector((store) => store.cart.items);
    const dispatch = useDispatch();

    const handleClearCart = () => {
        dispatch(clearCart());
    };

    const handleRemoveItem = (index) => {
        dispatch(removeItem(index));
    };

    return (
        <div className="cart">
            <h1>Cart ({cartItems.length} items)</h1>
            <div>
                {cartItems.length > 0 && (
                    <button onClick={handleClearCart} className="clear-cart">
                        Clear Cart
                    </button>
                )}

                {cartItems.length === 0 ? (
                    <h4>Cart is empty! Add items to the cart.</h4>
                ) : (
                    <div className="cart-items">
                        {cartItems.map((item, index) => (
                            <div key={index} className="cart-item">
                                <div className="cart-item-info">
                                    <h3>{item.name}</h3>
                                    <p className="cart-item-price">₹{item.price / 100}</p>
                                    <p className="cart-item-desc">{item.description}</p>
                                </div>
                                <div className="cart-item-image">
                                    {item.imageId && (
                                        <img
                                            src={`${swiggy_link_image}${item.imageId}`}
                                            alt={item.name}
                                        />
                                    )}
                                    <button
                                        onClick={() => handleRemoveItem(index)}
                                        className="remove-btn"
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default Cart;