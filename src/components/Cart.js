import { useDispatch, useSelector } from "react-redux"
import ItemsComponent from "./ItemsComponent"
import { clearCart } from "../utils/cartSlice"
const Cart=()=>{

    const cartItems=useSelector((store)=>store.cart.items)
    const dispatch=useDispatch()
    const handleClearCart=()=>{
        dispatch(clearCart())
    }
    return <div className="cart">
        <h1>Cart</h1>
       <div>
        <button onClick={handleClearCart} className="clear-cart">Clear Cart</button>
        <br></br>
        <br></br>
        {
            cartItems.length===0 && <h4>Cart is empty Add Items to the Cart!</h4>
        }
        <ItemsComponent itemCards={cartItems}/>
        </div>      
           { console.log(cartItems)} 
        </div>
}
export default Cart