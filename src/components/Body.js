import React, { useEffect } from "react";
import { useState } from "react";
import { RestaurantCard,withOpenLabel } from "./RestaurantCard";
import { fetchData } from "../utils/data";
import { Shimmer } from "./Shimmer";
import { Link } from "react-router-dom";
import { useOnlineStatus } from "../utils/useOnlineStatus";

export const Body = () => {
  const [swiggydata, setswiggydata] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [filterData,setFilteredData]=useState([]);
  const RestaurantCardOpenLabel=withOpenLabel(RestaurantCard);

  useEffect(() => {
    const getData = async () => {
      try {
        const data = await fetchData();
        setswiggydata(data);
        setFilteredData(data);
      } catch (error) {
        console.error("errot fetching data:", error);
      }
    };
    getData();
  }, []);

  const OnlineStatus=useOnlineStatus();
  if(OnlineStatus===false) return <h1>Looks like you are offline !! please check your internet</h1>

  if (swiggydata.length === 0) {
    return <Shimmer />;
  }

  const handleSearch = () => {
    const filter_data = swiggydata.filter((res) =>
        res.info.cuisines.some((cuisine) =>
            cuisine.toLowerCase().includes(searchText.toLowerCase())
        )
    );
    setFilteredData(filter_data);
    
};
  return (
    <div className="body">
      <div className="filter">
        <div className="search">
          <input
          placeholder="search item.."
            type="text"
            className="search-box"
            value={searchText}
            onChange={(e) => {
              setSearchText(e.target.value);
            }}
            onKeyPress={(e)=>{
              if(e.key==='Enter'){
                handleSearch();
              }
            }}
          />
          <button className="search-button"
            onClick={() => {
              //filter the restraunt cards and update
              // searchText

              console.log(searchText);
              handleSearch();
            }}
          >
            search
          </button>
        </div>
        <button
          className="filter-btn"
          onClick={() => {
            const filtered_data = swiggydata.filter(
              (res) => res.info.avgRatingString >4.5
            ); // Filter the data
            setFilteredData(filtered_data);
          }}
        >
          Top Rated Restaurant
        </button>
      </div>
      <div className="res-container">
        {filterData.map((restaurant, index) => (
          <Link to={"/restaurants/" + restaurant.info.id} key={restaurant.info.id}>
            {
              restaurant.info.type==="F"?(
                <RestaurantCardOpenLabel resData={restaurant} />
              ):(
                <RestaurantCard resData={restaurant} />
              )
            }
        </Link>
        ))}
      </div>
    </div>
  );
};
