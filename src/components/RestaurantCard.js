import { Body } from "./Body";
import { swiggy_link_image } from "../utils/constants";
import starImage from './star.png';
export const RestaurantCard = (props) => {
    return (
        <div className="res-card">
            <img className="res-logo" src={`${swiggy_link_image}${props.resData.info.cloudinaryImageId}`} alt="res-logo"></img>
            <h3>{props.resData.info.name}</h3>
            <h4>{props.resData.info.cuisines.join(", ")}</h4>
            <h4>{props.resData.info.costForTwo}</h4>
            <h4>rating {props.resData.info.avgRatingString} <img className="star" src={starImage} alt="star" /></h4>
            <h4>{props.resData.info.sla.slaString}</h4>
        </div>
    );
};

export const withOpenLabel=(RestaurantCard)=>{
    return(props)=>{
        return(
            <div>
                <label className="label">One Free Delievery</label>
                <RestaurantCard {...props}/>
            </div>
        )
    }
}