{"mappings": "AAAA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;AAKA;;;;;;;AASA;;;;;;;;AAgBA;;;;;;;;;;AASA;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;AAYA;;;;;AAOA;;;;;;AAOA;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;AAIA", "sources": ["style.css"], "sourcesContent": ["* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n    text-decoration: none;\n}\n\nbody {\n    font-family: Arial, Helvetica, sans-serif;\n    line-height: 1.6;\n    color: #333;\n    background-color: #f5f5f5;\n}\na{\n    color: inherit;\n    text-decoration: none;\n    \n}\n\n.header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center; \n    padding: 10px 20px;\n    border-bottom: 2px solid #ccc;\n    background-color: #4CAF50;\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n    position: sticky; \n    top: 0; \n    z-index: 1000; \n    height: 100px;\n}\n\n.logo {\n    width: 100px;\n}\n\n.nav-items {\n    padding: 0px 20px;\n}\n\n.nav-items > ul {\n    font-size: 18px;\n    display: flex;\n    list-style-type: none;\n    background-color: transparent;\n}\n\n.nav-items > ul > li {\n    padding: 10px 15px;\n    margin: 0 5px;\n    text-decoration: none;\n    color: white;\n    background-color: transparent;\n    transition: background-color 0.2s ease,color 0.3s ease;\n}\n.nav-items > ul > li:hover {\n    background-color: #45a049; \n    color: #fff; \n}\n.nav-items > ul > li a {\n    color: inherit;\n    text-decoration: none; \n}\n.nav-items > ul > li a:hover {\n    color: #fff; \n}\n\n.res-container {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 20px;\n    padding: 20px;\n}\n\n.res-card {\n    background-color: #ddd;\n    padding: 15px;\n    border-radius: 12px;\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    display: flex; \n    flex-direction: column; \n    justify-content: space-between; \n    text-align: center;\n    height: 450px; \n    width: 250px;\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\n    overflow: hidden;\n}\n\nh4, h3 {\n    color: rgb(29, 16, 29);\n    text-decoration: none;\n}\n\n.res-card:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);\n    cursor: pointer;\n    z-index: 0;\n}\n\n.res-logo {\n    width: 100%;\n    height: 200px;\n    object-fit: cover;\n    border-radius: 8px;\n    margin-bottom: 10px;\n}\n\n.res-title {\n    font-size: 1.2em;\n    font-weight: bold;\n    color: #333;\n    margin: 10px 0;\n    word-wrap: break-word;\n}\n\n.res-details {\n    font-size: 0.9em;\n    color: #777;\n    margin-bottom: 15px;\n    overflow-wrap: break-word;\n    flex-grow: 1; \n    overflow: hidden;\n    text-overflow: ellipsis; \n    display: -webkit-box; \n    text-decoration: none;\n}\n\n.res-rating {\n    font-size: 1em;\n    font-weight: bold;\n    color: #444;\n}\n\n.filter-btn {\n    margin: 10px;\n    cursor: pointer;\n}\n\n.shimmer-container {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 16px;\n    padding: 16px;\n}\n\n/* Individual shimmer card */\n.shimmer-card {\n    width: 100%;\n    height: 200px;\n    border-radius: 12px;\n    background: linear-gradient(\n        90deg,\n        #f0f0f0 25%,\n        #e0e0e0 50%,\n        #f0f0f0 75%\n    );\n    background-size: 200% 100%;\n    animation: shimmer 1.5s infinite;\n}\n\n/* Shimmer animation */\n@keyframes shimmer {\n    0% {\n        background-position: -200% 0;\n    }\n    100% {\n        background-position: 200% 0;\n    }\n}\n\n.login-btn {\n   \n    width: auto;\n    position: relative;\n    cursor: pointer;\n    background-color: transparent;\n    border: 0px;\n    font-size: 18px;\n}\n\n.filter {\n    display: flex;\n}\n\n.search-box {\n    width: 100%;\n    padding: 10px;\n    margin: 10px 5px;\n    border: 1px solid #ccc;\n    border-radius: 4px;\n    box-sizing: border-box;\n    font-size: 16px;\n    background-color: palegoldenrod;\n}\n.search-button{\n    margin-left: 5px;\n    padding: 10px 16px;\n    cursor: pointer;\n}\n.search-button:hover{\n    background-color: rgb(27, 81, 63);\n}\n\n\n.star {\n    width: 20px;\n    height: 23px;\n    object-fit: cover;\n}\n\n.menu {\n    max-width: 800px;\n    margin: 20px auto;\n    padding: 16px;\n    background-color: #ddd;\n    border-radius: 8px;\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n    border-left: 0;\n}\n\n\n.Menu-Card {\n    position: relative;\n    border: 1px solid #ddd;\n    border-radius: 8px;\n    margin: 16px 0;\n    padding: 16px;\n    background-color: #fff;\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n    position: relative;\n    cursor: pointer;\n}\n\n.Menu-Card h2 {\n    font-size: 18px;\n    margin-bottom: 12px;\n}\n\n/* Menu Item Styling */\n.Menu-item ul {\n    list-style-type: none;\n    padding: 0;\n    margin: 0;\n   \n}\n\n.Menu-item li {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    padding: 8px 0;\n    border-bottom: 1px solid #eee;\n   \n}\n\n.Menu-item .item {\n    flex: 1;\n}\n\n.Menu-item .item-name {\n    font-size: 14px;\n    font-weight: bold;\n}\n\n.Menu-item .rating {\n    font-size: 16px;\n    color: #555;\n    margin-top: 4px;\n}\n\n.Menu-item .desc {\n    font-size: 14px;\n    color: #777;\n    margin-top: 8px;\n}\n\n.Menu-item .item-image {\n    margin-left: 16px;\n}\n\n.Menu-item .item-image .image {\n    width: 80px;\n    height: 80px;\n    overflow: hidden;\n    border-radius: 8px;\n}\n\n.Menu-item .item-image .image img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\ni {\n    position: absolute;\n    top: 4px;\n    right: 10px;\n}\n.user-card{\n    padding: 10px;\n    margin: 4px;\n}\n .filter-btn{\n    background-color: gray; \n    color: white;\n    padding: 0px 20px;\n   height: 40px;\n    border: none;\n    border-radius: 4px;\n    cursor: pointer;\n    font-size: 10px;\n    transition: background-color 0.3s ease;\n}\n.label{\n    position: absolute;\n    background-color: black;\n    color: white;\n    z-index: 1;\n    border-radius: 2px;\n}\n.recommended{\n    background-color: #ddddddf8;\n}\n.AddButton{\n    padding: 4px 12px;\n    background-color: greenyellow;\n    border: none;\n    margin: auto;\n    position: absolute;\n}"], "names": [], "version": 3, "file": "index.3a01df49.css.map", "sourceRoot": "/__parcel_source_root/"}