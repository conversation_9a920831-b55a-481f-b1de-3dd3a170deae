* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  text-decoration: none;
}

body {
  color: #333;
  background-color: #f5f5f5;
  font-family: Arial, Helvetica, sans-serif;
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

.header {
  z-index: 1000;
  background-color: #4caf50;
  border-bottom: 2px solid #ccc;
  justify-content: space-between;
  align-items: center;
  height: 100px;
  padding: 10px 20px;
  display: flex;
  position: sticky;
  top: 0;
  box-shadow: 0 2px 5px #0000001a;
}

.logo {
  width: 100px;
}

.nav-items {
  padding: 0 20px;
}

.nav-items > ul {
  background-color: #0000;
  font-size: 18px;
  list-style-type: none;
  display: flex;
}

.nav-items > ul > li {
  color: #fff;
  background-color: #0000;
  margin: 0 5px;
  padding: 10px 15px;
  text-decoration: none;
  transition: background-color .2s, color .3s;
}

.nav-items > ul > li:hover {
  color: #fff;
  background-color: #45a049;
}

.nav-items > ul > li a {
  color: inherit;
  text-decoration: none;
}

.nav-items > ul > li a:hover {
  color: #fff;
}

.res-container {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px;
  display: grid;
}

.res-card {
  text-align: center;
  background-color: #ddd;
  border-radius: 12px;
  flex-direction: column;
  justify-content: space-between;
  width: 250px;
  height: 450px;
  padding: 15px;
  transition: transform .3s, box-shadow .3s;
  display: flex;
  overflow: hidden;
  box-shadow: 0 4px 6px #0000001a;
}

h4, h3 {
  color: #1d101d;
  text-decoration: none;
}

.res-card:hover {
  cursor: pointer;
  z-index: 0;
  transform: translateY(-5px);
  box-shadow: 0 8px 12px #0003;
}

.res-logo {
  object-fit: cover;
  border-radius: 8px;
  width: 100%;
  height: 200px;
  margin-bottom: 10px;
}

.res-title {
  color: #333;
  word-wrap: break-word;
  margin: 10px 0;
  font-size: 1.2em;
  font-weight: bold;
}

.res-details {
  color: #777;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  flex-grow: 1;
  margin-bottom: 15px;
  font-size: .9em;
  text-decoration: none;
  display: -webkit-box;
  overflow: hidden;
}

.res-rating {
  color: #444;
  font-size: 1em;
  font-weight: bold;
}

.filter-btn {
  cursor: pointer;
  margin: 10px;
}

.shimmer-container {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding: 16px;
  display: grid;
}

.shimmer-card {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 12px;
  width: 100%;
  height: 200px;
  animation: 1.5s infinite shimmer;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.login-btn {
  cursor: pointer;
  background-color: #0000;
  border: 0;
  width: auto;
  font-size: 18px;
  position: relative;
}

.filter {
  display: flex;
}

.search-box {
  box-sizing: border-box;
  background-color: #eee8aa;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  margin: 10px 5px;
  padding: 10px;
  font-size: 16px;
}

.search-button {
  cursor: pointer;
  margin-left: 5px;
  padding: 10px 16px;
}

.search-button:hover {
  background-color: #1b513f;
}

.star {
  object-fit: cover;
  width: 20px;
  height: 23px;
}

.menu {
  background-color: #ddd;
  border-left: 0;
  border-radius: 8px;
  max-width: 800px;
  margin: 20px auto;
  padding: 16px;
  box-shadow: 0 4px 8px #0000001a;
}

.Menu-Card {
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 16px 0;
  padding: 16px;
  position: relative;
  box-shadow: 0 4px 8px #0000001a;
}

.Menu-Card h2 {
  margin-bottom: 12px;
  font-size: 18px;
}

.Menu-item ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.Menu-item li {
  border-bottom: 1px solid #eee;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  display: flex;
}

.Menu-item .item {
  flex: 1;
}

.Menu-item .item-name {
  font-size: 14px;
  font-weight: bold;
}

.Menu-item .rating {
  color: #555;
  margin-top: 4px;
  font-size: 16px;
}

.Menu-item .desc {
  color: #777;
  margin-top: 8px;
  font-size: 14px;
}

.Menu-item .item-image {
  margin-left: 16px;
}

.Menu-item .item-image .image {
  border-radius: 8px;
  width: 80px;
  height: 80px;
  overflow: hidden;
}

.Menu-item .item-image .image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

i {
  position: absolute;
  top: 4px;
  right: 10px;
}

.user-card {
  margin: 4px;
  padding: 10px;
}

.filter-btn {
  color: #fff;
  cursor: pointer;
  background-color: gray;
  border: none;
  border-radius: 4px;
  height: 40px;
  padding: 0 20px;
  font-size: 10px;
  transition: background-color .3s;
}

.label {
  color: #fff;
  z-index: 1;
  background-color: #000;
  border-radius: 2px;
  position: absolute;
}

.recommended {
  background-color: #ddddddf8;
}

.AddButton {
  cursor: pointer;
  background-color: #adff2f;
  border: none;
  border-radius: 4px;
  margin: auto;
  padding: 4px 12px;
  font-weight: bold;
  transition: background-color .3s;
  position: absolute;
}

.AddButton:hover {
  background-color: #9acd32;
}

.cart-container {
  position: relative;
}

.cart-link {
  align-items: center;
  gap: 5px;
  display: flex;
  position: relative;
}

.cart-count {
  color: #fff;
  background-color: #f44;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  min-width: 18px;
  height: 18px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  animation: .3s bounce;
  display: flex;
  position: absolute;
  top: -8px;
  right: -8px;
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-3px);
  }

  80% {
    transform: translateY(-1px);
  }
}

.cart {
  background-color: #fff;
  border-radius: 8px;
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  box-shadow: 0 4px 8px #0000001a;
}

.cart h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.clear-cart {
  color: #fff;
  cursor: pointer;
  background-color: #f44;
  border: none;
  border-radius: 4px;
  margin-bottom: 20px;
  padding: 10px 20px;
  font-weight: bold;
  transition: background-color .3s;
}

.clear-cart:hover {
  background-color: #c33;
}

.cart-items {
  flex-direction: column;
  gap: 15px;
  display: flex;
}

.cart-item {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  display: flex;
  box-shadow: 0 2px 4px #0000001a;
}

.cart-item-info {
  flex: 1;
  margin-right: 15px;
}

.cart-item-info h3 {
  color: #333;
  margin: 0 0 8px;
  font-size: 18px;
}

.cart-item-price {
  color: #4caf50;
  margin: 5px 0;
  font-size: 16px;
  font-weight: bold;
}

.cart-item-desc {
  color: #666;
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.4;
}

.cart-item-image {
  flex-direction: column;
  align-items: center;
  gap: 10px;
  display: flex;
}

.cart-item-image img {
  object-fit: cover;
  border-radius: 8px;
  width: 80px;
  height: 80px;
}

.remove-btn {
  color: #fff;
  cursor: pointer;
  background-color: #f44;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: bold;
  transition: background-color .3s;
}

.remove-btn:hover {
  background-color: #c33;
}
/*# sourceMappingURL=index.3a01df49.css.map */
